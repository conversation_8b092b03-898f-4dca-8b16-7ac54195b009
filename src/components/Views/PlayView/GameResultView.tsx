import { useEffect, useState } from "react";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { PrimaryButton } from "microapps";
import "./GameResultView.scss";

interface GameResult {
  winner: "ai" | "user" | "draw";
  finalGuess?: string;
  wasCorrect?: boolean;
  currentCharacter?: string;
  questionsUsed: number;
  maxQuestions: number;
  mode: "ia_vs_player";
  endTime: Date;
}

interface GameResultViewProps {
  onPlayAgain: () => void;
  onBackToMain: () => void;
}

const GameResultView: React.FC<GameResultViewProps> = ({
  onPlayAgain,
  onBackToMain,
}) => {
  const { session } = useEnygmaGame();
  const [gameResult, setGameResult] = useState<GameResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasSpokenResult, setHasSpokenResult] = useState(false);

  // Cargar resultado del juego
  useEffect(() => {
    const loadGameResult = () => {
      try {
        // Primero intentar obtener del contexto de sesión actual
        if (session && session.phase === "finished") {
          const result: GameResult = {
            winner: session.winner || "draw",
            finalGuess: session.finalGuess,
            wasCorrect: session.wasCorrect,
            currentCharacter: session.currentCharacter,
            questionsUsed: session.questionCount,
            maxQuestions: session.maxQuestions,
            mode: session.mode,
            endTime: session.endTime || new Date(),
          };
          setGameResult(result);

          // Guardar también en localStorage para persistencia
          localStorage.setItem(
            "enygma_last_game_result",
            JSON.stringify(result)
          );
        } else {
          // Si no hay sesión activa, intentar cargar de localStorage
          const savedResult = localStorage.getItem("enygma_last_game_result");
          if (savedResult) {
            const parsed = JSON.parse(savedResult);
            // Convertir fecha de string a Date
            parsed.endTime = new Date(parsed.endTime);
            setGameResult(parsed);
          }
        }
      } catch (error) {
        console.error("Error cargando resultado del juego:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGameResult();
  }, [session]);

  // Narrar resultado automáticamente
  useEffect(() => {
    if (gameResult && !hasSpokenResult) {
      const narrateResult = async () => {
        try {
          setHasSpokenResult(true);
        } catch (error) {
          console.error("Error narrando resultado:", error);
          setHasSpokenResult(true); // Marcar como narrado para evitar bucles
        }
      };

      // Esperar un poco antes de narrar para que la vista se renderice
      const timer = setTimeout(narrateResult, 1000);
      return () => clearTimeout(timer);
    }
  }, [gameResult, hasSpokenResult]);

  const getResultMessage = (): string => {
    if (!gameResult) return "";

    const { winner, finalGuess, currentCharacter, questionsUsed, mode } =
      gameResult;

    if (winner === "user") {
      if (mode === "ia_vs_player") {
        return `¡Felicidades! Has adivinado correctamente. El personaje era ${currentCharacter}. Lo conseguiste en ${questionsUsed} pregunta${questionsUsed !== 1 ? "s" : ""}. ¡Excelente deducción!`;
      } else {
        return `¡Bien hecho! Lograste mantener el misterio. No pude adivinar que el personaje era ${currentCharacter}. Tu estrategia fue muy efectiva.`;
      }
    } else if (winner === "ai") {
      return `Se agotaron las preguntas. El personaje era ${currentCharacter}. Estuvo muy cerca, pero el misterio se mantiene esta vez. ¿Quieres intentarlo de nuevo?`;
    } else {
      return `¡Qué partida tan interesante! Terminamos en empate. Ambos mostramos nuestras habilidades. ¿Listos para el desempate?`;
    }
  };

  const getResultTitle = (): string => {
    if (!gameResult) return "";

    const { winner, mode } = gameResult;

    if (winner === "user") {
      return mode === "ia_vs_player" ? "¡Has ganado!" : "¡Victoria!";
    } else if (winner === "ai") {
      return mode === "ia_vs_player"
        ? "¡Mejor suerte la próxima vez!"
        : "¡He ganado!";
    } else {
      return "¡Empate!";
    }
  };

  const getResultSubtitle = (): string => {
    if (!gameResult) return "";

    const {
      winner,
      finalGuess,
      currentCharacter,
      questionsUsed,
      maxQuestions,
      mode,
    } = gameResult;

    if (winner === "user") {
      if (mode === "ia_vs_player") {
        return `Adivinaste "${currentCharacter}" en ${questionsUsed}/${maxQuestions} preguntas`;
      } else {
        return `Mantuviste el secreto de "${currentCharacter}"`;
      }
    } else if (winner === "ai") {
      if (mode === "ia_vs_player") {
        return `El personaje era "${currentCharacter}"`;
      } else {
        return `Adiviné que era "${finalGuess}"`;
      }
    } else {
      return `Ambos demostramos nuestras habilidades`;
    }
  };

  const getResultEmoji = (): string => {
    if (!gameResult) return "🎭";

    const { winner } = gameResult;

    if (winner === "user") {
      return "🎉";
    } else if (winner === "ai") {
      return "🤖";
    } else {
      return "🤝";
    }
  };

  const getResultColor = (): string => {
    if (!gameResult) return "#88FFD5";

    const { winner } = gameResult;

    if (winner === "user") {
      return "#4ade80"; // Verde para victoria del usuario
    } else if (winner === "ai") {
      return "#f59e0b"; // Amarillo/naranja para victoria de la IA
    } else {
      return "#88FFD5"; // Color por defecto para empate
    }
  };

  const handlePlayAgain = () => {
    // Limpiar resultado guardado
    localStorage.removeItem("enygma_last_game_result");
    setHasSpokenResult(false);
    onPlayAgain();
  };

  const handleBackToMain = () => {
    // Limpiar resultado guardado
    localStorage.removeItem("enygma_last_game_result");
    setHasSpokenResult(false);
    onBackToMain();
  };

  if (isLoading) {
    return (
      <div className="content game-result-modal">
        <div className="result-container loading">
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          <p>Analizando los resultados...</p>
        </div>
      </div>
    );
  }

  if (!gameResult) {
    return (
      <div className="content game-result-modal">
        <div className="result-container error">
          <h2>Error</h2>
          <p>No se pudieron cargar los resultados del juego.</p>
          <PrimaryButton
            onClick={handleBackToMain}
            text="Volver al menú"
            backgroundColor="#88FFD5"
            textColor="#001428"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="content game-result-modal">
      <div className="result-container">
        {/* Emoji animado */}
        <div className="result-emoji" style={{ color: getResultColor() }}>
          {getResultEmoji()}
        </div>

        {/* Título principal */}
        <h1 className="result-title" style={{ color: getResultColor() }}>
          {getResultTitle()}
        </h1>

        {/* Subtítulo con detalles */}
        <p className="result-subtitle">{getResultSubtitle()}</p>

        {/* Mensaje descriptivo */}
        <div className="result-message">
          <p>{getResultMessage()}</p>
        </div>

        {/* Estadísticas del juego */}
        <div className="game-stats">
          <div className="stat-item">
            <span className="stat-label">Preguntas usadas:</span>
            <span className="stat-value">
              {gameResult.questionsUsed}/{gameResult.maxQuestions}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Modo de juego:</span>
            <span className="stat-value">
              {gameResult.mode === "ia_vs_player"
                ? "IA vs Jugador"
                : "Jugador vs IA"}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Duración:</span>
            <span className="stat-value">
              {Math.floor(
                (gameResult.endTime.getTime() -
                  (session?.startTime?.getTime() ||
                    gameResult.endTime.getTime())) /
                  60000
              )}{" "}
              min
            </span>
          </div>
        </div>

        {/* Personaje revelado */}
        {gameResult.currentCharacter && (
          <div className="character-reveal">
            <h3>El personaje era:</h3>
            <div className="character-name">{gameResult.currentCharacter}</div>
          </div>
        )}

        {/* Botones de acción */}
        <div className="result-actions">
          <PrimaryButton
            onClick={handlePlayAgain}
            text="Jugar de nuevo"
            backgroundColor={getResultColor()}
            textColor="#001428"
            borderRadius="8px"
          />

          <button onClick={handleBackToMain} className="secondary-button">
            Volver al menú
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameResultView;
