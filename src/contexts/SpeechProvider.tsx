import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useRef,
  type ReactNode,
} from "react";
import { speechService } from "../services/SpeechService";
import { audioManager } from "../services/AudioManager";
import { speechCoordinator } from "../services/SpeechCoordinator";
import type {
  VoiceGender,
  MessageType,
  SpeechMessage,
  SpeechInputState,
  SpeechOutputState,
} from "../models";

/**
 * ========================================================================
 * SpeechProvider - Contexto para manejo de entrada y salida de voz
 *
 * Gestiona:
 * - Speech Input - Reconocimiento y validación de voz
 * - Speech Output - Síntesis de voz y reproducción
 * - Audio Management - Música de fondo y efectos
 * - Queue Management - Colas de mensajes de audio
 * - Game Integration - Funciones específicas del juego
 * =======================================================================
 */

export interface UnifiedSpeechContextProps {
  // ========== CURRENT STATES ==========
  inputState: SpeechInputState;
  outputState: SpeechOutputState;
  isInitialized: boolean;
  globalError: string | null;

  // ========== INPUT FUNCTIONALITY ==========
  waitForCustomResponse: (
    expectedResponses: string[],
    timeout?: number
  ) => Promise<string>;
  normalizeText: (text: string) => Promise<string>;

  // ========== OUTPUT FUNCTIONALITY - Basic Speech Controls ==========
  speak: (text: string) => Promise<void>;
  pause: () => void;
  resume: () => void;
  stop: () => void;

  // ========== OUTPUT FUNCTIONALITY - Music Controls ==========
  playBackgroundMusic: () => Promise<void>;
  pauseMusic: () => void;
  resumeMusic: () => void;
  stopMusic: () => void;
  setMusicVolume: (volume: number) => void;

  // ========== OUTPUT FUNCTIONALITY - Speech Controls ==========
  pauseSpeech: () => void;
  resumeSpeech: () => void;
  stopSpeech: () => void;
  setSpeechVolume: (volume: number) => void;

  // ========== OUTPUT FUNCTIONALITY - Azure TTS Controls ==========
  enableAzureTTS: () => void;
  disableAzureTTS: () => void;
  toggleAzureTTS: () => boolean;
  isAzureTTSEnabled: () => boolean;

  // ========== OUTPUT FUNCTIONALITY - Master Controls ==========
  toggleMute: () => boolean;
  pauseAll: () => void;
  resumeAll: () => void;
  stopAll: () => void;

  // ========== OUTPUT FUNCTIONALITY - Configuration ==========
  configure: (gender: VoiceGender) => Promise<boolean>;

  // ========== OUTPUT FUNCTIONALITY - Queue Management ==========
  clearQueue: () => void;
  skipCurrent: () => void;

  // ========== OUTPUT FUNCTIONALITY - Utilities ==========
  getAvailableVoices: () => string[];
  reset: () => void;

  // ========== OUTPUT FUNCTIONALITY - Message History ==========
  messageHistory: SpeechMessage[];
  getLastMessage: () => SpeechMessage | null;
  replayLastMessage: () => Promise<void>;
}

// ========== UNIFIED STATE TYPE ==========
interface UnifiedSpeechState {
  // Input state
  input: SpeechInputState;

  // Output state
  output: SpeechOutputState;

  // Shared state
  isInitialized: boolean;
  globalError: string | null;
}

const SpeechContext = createContext<UnifiedSpeechContextProps | undefined>(
  undefined
);

/**
 * Hook personalizado para acceder al contexto de Speech
 * Incluye validación de uso dentro del Provider
 *
 * @returns {UnifiedSpeechContextProps} Propiedades y métodos del contexto
 * @throws {Error} Si se usa fuera del SpeechProvider
 */
export const useSpeech = () => {
  const context = useContext(SpeechContext);
  if (!context) {
    throw new Error("useSpeech must be used within SpeechProvider");
  }
  return context;
};

export const useSpeechInput = () => {
  const {
    inputState,
    waitForCustomResponse,
    normalizeText,
  } = useSpeech();

  return {
    state: inputState,
    waitForCustomResponse,
    normalizeText,
  };
};

export const useSpeechOutput = () => {
  const {
    outputState,
    messageHistory,
    speak,
    pause,
    resume,
    stop,
    playBackgroundMusic,
    pauseMusic,
    resumeMusic,
    stopMusic,
    setMusicVolume,
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
    setSpeechVolume,
    enableAzureTTS,
    disableAzureTTS,
    toggleAzureTTS,
    isAzureTTSEnabled,
    toggleMute,
    pauseAll,
    resumeAll,
    stopAll,
    configure,
    clearQueue,
    skipCurrent,
    getAvailableVoices,
    reset,
    getLastMessage,
    replayLastMessage,
  } = useSpeech();

  return {
    state: outputState,
    messageHistory,
    speak,
    pause,
    resume,
    stop,
    playBackgroundMusic,
    pauseMusic,
    resumeMusic,
    stopMusic,
    setMusicVolume,
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
    setSpeechVolume,
    enableAzureTTS,
    disableAzureTTS,
    toggleAzureTTS,
    isAzureTTSEnabled,
    toggleMute,
    pauseAll,
    resumeAll,
    stopAll,
    configure,
    clearQueue,
    skipCurrent,
    getAvailableVoices,
    reset,
    getLastMessage,
    replayLastMessage,
  };
};

/**
 * Provider principal del juego Enygma
 * Debe envolver toda la lógica de juego y ser el contexto más interno
 *
 * @param {Object} props - Props del componente
 * @param {ReactNode} props.children - Componentes hijos
 */
export const SpeechProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<UnifiedSpeechState>({
    input: {
      isListening: false,
      isProcessing: false,
      confidence: 0,
      lastValidatedResponse: null,
      errorMessage: null,
    },
    output: {
      isReady: false,
      isConfiguring: false,
      playbackState: "idle",
      currentVoice: "",
      availableVoices: [],
      errorMessage: null,
      audioState: audioManager.getState(),
      isMusicPlaying: false,
      isSpeechPlaying: false,
    },
    isInitialized: false,
    globalError: null,
  });

  // ========== OUTPUT-SPECIFIC STATE ==========
  const [messageHistory, setMessageHistory] = useState<SpeechMessage[]>([]);
  const [messageQueue, setMessageQueue] = useState<string[]>([]);

  // ========== REFS ==========
  const abortControllerRef = useRef<AbortController | null>(null);

  // ========== INITIALIZATION EFFECT ==========
  useEffect(() => {
    console.log(
      "ℹ️ [SpeechProvider] 🚀 Inicializando SpeechProvider unificado"
    );

    // Inicializar estado desde servicios
    updateStateFromServices();

    // Suscribirse a cambios del AudioManager
    const removeAudioListener = audioManager.addListener((audioState) => {
      setState((prev) => ({
        ...prev,
        output: {
          ...prev.output,
          audioState,
          isMusicPlaying: audioState.music.isPlaying,
          isSpeechPlaying: audioState.speech.isPlaying,
        },
      }));
    });

    // Marcar como inicializado
    setState((prev) => ({ ...prev, isInitialized: true }));

    return () => {
      console.log("ℹ️ [SpeechProvider] 🧹 Limpiando SpeechProvider");
      removeAudioListener();
      stopAll();
      clearQueue();
    };
  }, []);

  // ========== HELPER FUNCTIONS ==========
  const updateStateFromServices = useCallback(() => {
    setState((prev) => ({
      ...prev,
      output: {
        ...prev.output,
        currentVoice: speechService.getCurrentVoiceId(),
        availableVoices: speechService.getAvailableVoicesList(),
        isReady: Boolean(speechService.getCurrentVoiceId()),
        audioState: audioManager.getState(),
      },
    }));
  }, []);

  const createMessage = useCallback(
    (text: string, type: MessageType): SpeechMessage => {
      return {
        id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        text,
        type,
        timestamp: new Date(),
        voice: state.output.currentVoice,
      };
    },
    [state.output.currentVoice]
  );

  const addToHistory = useCallback((message: SpeechMessage) => {
    setMessageHistory((prev) => [message, ...prev.slice(0, 49)]); // Keep last 50 messages
  }, []);

  const updateOutputState = useCallback(
    (updates: Partial<SpeechOutputState>) => {
      setState((prev) => ({
        ...prev,
        output: { ...prev.output, ...updates },
      }));
    },
    []
  );

  // ========== INPUT FUNCTIONALITY ==========
  const waitForCustomResponse = useCallback(
    (expectedResponses: string[], timeout: number = 30000): Promise<string> => {
      return new Promise((resolve, reject) => {
        const abortController = new AbortController();
        abortControllerRef.current = abortController;

        let resolved = false;

        const timeoutId = setTimeout(() => {
          if (!resolved) {
            resolved = true;
            abortController.abort();
            reject(new Error("Timeout esperando respuesta específica"));
          }
        }, timeout);

        // Simular respuesta que coincide con la primera respuesta esperada
        setTimeout(() => {
          if (!resolved) {
            resolved = true;
            clearTimeout(timeoutId);
            const simulatedResponse = expectedResponses[0] ?? "";
            resolve(simulatedResponse);
          }
        }, 1000);

        abortController.signal.addEventListener("abort", () => {
          if (!resolved) {
            resolved = true;
            clearTimeout(timeoutId);
            reject(new Error("Operación cancelada"));
          }
        });
      });
    },
    []
  );

  const normalizeText = useCallback(async (text: string): Promise<string> => {
    // TODO: Implementar normalización real
    return text;
  }, []);

  // ========== OUTPUT FUNCTIONALITY - MUSIC CONTROLS ==========
  const playBackgroundMusic = useCallback(async (): Promise<void> => {
    try {
      await audioManager.playMusic("/assets/sounds/sound.mp3");
      console.log("✅ [SpeechProvider] 🎵 Música de fondo iniciada");
    } catch (error) {
      console.error(
        "❌ [SpeechProvider] Error iniciando música de fondo",
        error
      );
      throw error;
    }
  }, []);

  const pauseMusic = useCallback(() => {
    audioManager.pauseMusic();
  }, []);

  const resumeMusic = useCallback(() => {
    audioManager.resumeMusic();
  }, []);

  const stopMusic = useCallback(() => {
    audioManager.stopMusic();
  }, []);

  const setMusicVolume = useCallback((volume: number) => {
    audioManager.setMusicVolume(volume);
  }, []);

  // ========== OUTPUT FUNCTIONALITY - SPEECH CONTROLS ==========
  const pauseSpeech = useCallback(() => {
    audioManager.pauseSpeech();
  }, []);

  const resumeSpeech = useCallback(() => {
    audioManager.resumeSpeech();
  }, []);

  const stopSpeech = useCallback(() => {
    audioManager.stopSpeech();
  }, []);

  const setSpeechVolume = useCallback((volume: number) => {
    audioManager.setSpeechVolume(volume);
  }, []);

  // ========== OUTPUT FUNCTIONALITY - AZURE TTS CONTROLS ==========
  const enableAzureTTS = useCallback(() => {
    speechService.enableAzureTTS();
  }, []);

  const disableAzureTTS = useCallback(() => {
    speechService.disableAzureTTS();
  }, []);

  const toggleAzureTTS = useCallback((): boolean => {
    return speechService.toggleAzureTTS();
  }, []);

  const isAzureTTSEnabled = useCallback((): boolean => {
    return speechService.isAzureTTSEnabled();
  }, []);

  // ========== OUTPUT FUNCTIONALITY - MASTER CONTROLS ==========
  const toggleMute = useCallback((): boolean => {
    return audioManager.toggleMute();
  }, []);

  const pauseAll = useCallback(() => {
    audioManager.pauseAll();
  }, []);

  const resumeAll = useCallback(() => {
    audioManager.resumeAll();
  }, []);

  const stopAll = useCallback(() => {
    audioManager.stopAll();
  }, []);

  // ========== OUTPUT FUNCTIONALITY - SPEECH ==========
  const speak = useCallback(
    async (text: string): Promise<void> => {
      if (!text.trim()) {
        console.warn("⚠️ [SpeechProvider] ⚠️ Texto vacío para speak()");
        return;
      }

      updateOutputState({ playbackState: "loading", errorMessage: null });

      try {
        const startTime = Date.now();
        const message = createMessage(text, "system");

        await speechCoordinator.speak(text, "info", {
          onStart: () => {
            updateOutputState({ playbackState: "playing" });
          },
          onComplete: () => {
            const duration = Date.now() - startTime;
            message.duration = duration;
            addToHistory(message);
            updateOutputState({ playbackState: "idle" });
            console.log("✅ [SpeechProvider] ✅ Speech completado", {
              duration,
            });
          },
          onError: (error) => {
            updateOutputState({
              playbackState: "error",
              errorMessage: error.message,
            });
            console.error("❌ [SpeechProvider] ❌ Error en speech", error);
          },
        });
      } catch (error) {
        updateOutputState({
          playbackState: "error",
          errorMessage:
            error instanceof Error ? error.message : "Error desconocido",
        });
        console.error("❌ [SpeechProvider] ❌ Error en speak()", error);
        throw error;
      }
    },
    [createMessage, addToHistory, updateOutputState]
  );

  const pause = useCallback(() => {
    pauseSpeech();
    updateOutputState({ playbackState: "paused" });
  }, [pauseSpeech, updateOutputState]);

  const resume = useCallback(() => {
    resumeSpeech();
    updateOutputState({ playbackState: "playing" });
  }, [resumeSpeech, updateOutputState]);

  const stop = useCallback(() => {
    stopSpeech();
    updateOutputState({ playbackState: "idle" });
  }, [stopSpeech, updateOutputState]);

  // ========== OUTPUT FUNCTIONALITY - CONFIGURATION ==========
  const configure = useCallback(
    async (gender: VoiceGender): Promise<boolean> => {
      console.log(`ℹ️ [SpeechProvider] 🔧 Configurando voz: ${gender}`);

      updateOutputState({ isConfiguring: true, errorMessage: null });

      try {
        const success = await speechService.configVoice(gender);

        if (success) {
          updateStateFromServices();
          console.log(`✅ [SpeechProvider] ✅ Voz configurada: ${gender}`);
        } else {
          throw new Error(`No se pudo configurar voz ${gender}`);
        }

        return success;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Error configurando voz";
        console.error("❌ [SpeechProvider] ❌ Error configurando voz", error);

        updateOutputState({ errorMessage });
        return false;
      } finally {
        updateOutputState({ isConfiguring: false });
      }
    },
    [updateStateFromServices, updateOutputState]
  );

  // ========== OUTPUT FUNCTIONALITY - QUEUE MANAGEMENT ==========
  const clearQueue = useCallback(() => {
    console.log("ℹ️ [SpeechProvider] 🗑️ Limpiando cola de mensajes");
    setMessageQueue([]);
    speechCoordinator.clearQueue();
  }, []);

  const skipCurrent = useCallback(() => {
    console.log("ℹ️ [SpeechProvider] ⏭️ Saltando mensaje actual");
    speechCoordinator.interrupt("critical");
    if (messageQueue.length > 1) {
      setMessageQueue((prev) => prev.slice(1));
    } else {
      clearQueue();
    }
    updateOutputState({ playbackState: "idle" });
  }, [messageQueue, clearQueue, updateOutputState]);

  // ========== OUTPUT FUNCTIONALITY - UTILITIES ==========
  const getAvailableVoices = useCallback((): string[] => {
    return speechService.getAvailableVoicesList();
  }, []);

  const reset = useCallback(() => {
    console.log("ℹ️ [SpeechProvider] 🔄 Reseteando SpeechProvider");

    stopAll();
    clearQueue();

    setState((prev) => ({
      ...prev,
      input: {
        isListening: false,
        isProcessing: false,
        confidence: 0,
        lastValidatedResponse: null,
        errorMessage: null,
      },
      output: {
        isReady: false,
        isConfiguring: false,
        playbackState: "idle",
        currentVoice: "",
        availableVoices: [],
        errorMessage: null,
        audioState: audioManager.getState(),
        isMusicPlaying: false,
        isSpeechPlaying: false,
      },
      globalError: null,
    }));

    setMessageHistory([]);
  }, [stopAll, clearQueue]);

  // ========== OUTPUT FUNCTIONALITY - MESSAGE HISTORY ==========
  const getLastMessage = useCallback((): SpeechMessage | null => {
    return messageHistory[0] || null;
  }, [messageHistory]);

  const replayLastMessage = useCallback(async (): Promise<void> => {
    const lastMessage = getLastMessage();
    if (lastMessage) {
      console.log("ℹ️ [SpeechProvider] 🔁 Repitiendo último mensaje");
      await speak(lastMessage.text);
    } else {
      console.warn("⚠️ [SpeechProvider] ⚠️ No hay mensajes para repetir");
    }
  }, [getLastMessage, speak]);

  // ========== CONTEXT VALUE ==========
  const contextValue: UnifiedSpeechContextProps = {
    // States
    inputState: state.input,
    outputState: state.output,
    isInitialized: state.isInitialized,
    globalError: state.globalError,

    waitForCustomResponse,
    normalizeText,

    // Output functionality - Basic speech controls
    speak,
    pause,
    resume,
    stop,

    // Output functionality - Music controls
    playBackgroundMusic,
    pauseMusic,
    resumeMusic,
    stopMusic,
    setMusicVolume,

    // Output functionality - Speech controls
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
    setSpeechVolume,

    // Output functionality - Azure TTS controls
    enableAzureTTS,
    disableAzureTTS,
    toggleAzureTTS,
    isAzureTTSEnabled,

    // Output functionality - Master controls
    toggleMute,
    pauseAll,
    resumeAll,
    stopAll,

    // Output functionality - Configuration
    configure,

    // Output functionality - Queue management
    clearQueue,
    skipCurrent,

    // Output functionality - Utilities
    getAvailableVoices,
    reset,

    // Output functionality - Message history
    messageHistory,
    getLastMessage,
    replayLastMessage,
  };

  return (
    <SpeechContext.Provider value={contextValue}>
      {children}
    </SpeechContext.Provider>
  );
};
